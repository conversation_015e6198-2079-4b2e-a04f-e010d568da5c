'use client';

import { useParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import CommissionerProfileView from '../../../../../components/user-profiles/recruiter/commissioner-profile-view';

export default function CommissionerProfilePage() {
  const params = useParams();
  const { data: session } = useSession();

  const userId = params?.id as string;
  const currentUserId = session?.user?.id;
  const isOwnProfile = userId === currentUserId;
  const currentUserType = (session?.user as any)?.userType;

  return (
    <CommissionerProfileView
      userId={userId}
      isOwnProfile={isOwnProfile}
      viewerUserType={currentUserType || 'commissioner'}
      profileType="commissioner"
    />
  );
}